import { useEffect, useState, useCallback } from "react";
import Map from "react-map-gl/mapbox";
import InternalSearchControlMap from "./controls/internal-search-control-map";
import InternalGlobalControlMap from "./controls/internal-global-control-map";
import InternalWorkforcesCluster from "./layers/internal-workforces-cluster";
import { useOutletContext } from "react-router-dom";
import InternalWorkforcesMarkers from "./markers/internal-workfoces-markers";
import InternalSitesMarkers from "./markers/internal-sites-markers";
import InternalSitesCluster from "./layers/internal-sites-cluster";
import InternalSitesHeatmap from "./layers/internal-sites-heatmap";

export default function InternalMap({
  mapRef,
  theme,
  setCenter,
  sidebarOpen,
  display = ["sitesMarkers"],
}) {
  const { workforces, sites, entities } = useOutletContext();
  const [mapStyle, setMapStyle] = useState(
    theme === "light"
      ? "mapbox://styles/tsinovec/cmdrg8u78001d01sd5evibo4x"
      : "mapbox://styles/tsinovec/cmdrfkyi5008801r5ckbg9zvf"
  );

  useEffect(() => {
    setMapStyle(
      theme === "light"
        ? "mapbox://styles/tsinovec/cmdrg8u78001d01sd5evibo4x"
        : "mapbox://styles/tsinovec/cmdrfkyi5008801r5ckbg9zvf"
    );
  }, [theme]);

  useEffect(() => {
    mapRef.current?.resize();
  }, [sidebarOpen]);

  return (
    <>
      <Map
        attributionControl={false}
        onLoad={(target) => {
          mapRef.current = target.target;
          setCenter(target.target.getCenter());
        }}
        onMove={(target) => setCenter(target.target.getCenter())}
        ref={mapRef}
        mapStyle={mapStyle}
        mapboxAccessToken={import.meta.env.VITE_MAPBOX_TOKEN}
        initialViewState={{
          longitude: 3,
          latitude: 47,
          zoom: 5.5,
          bearing: 0,
          pitch: 0,
        }}
      >
        <InternalSitesHeatmap sites={sites} visible />
        <InternalSitesMarkers
          entities={entities}
          theme={theme}
          visible={display.includes("sitesMarkers")}
          sites={sites}
        />
        <InternalSitesCluster
          sites={sites}
          visible={display.includes("sitesCluster")}
        />
        <InternalWorkforcesMarkers
          workforces={workforces}
          visible={display.includes("workforcesMarkers")}
        />
        <InternalWorkforcesCluster
          workforces={workforces}
          visible={display.includes("workforcesCluster")}
        />
        <InternalGlobalControlMap
          theme={theme}
          mapRef={mapRef}
          visible={true}
        />
        <InternalSearchControlMap
          theme={theme}
          mapRef={mapRef}
          visible={true}
        />
      </Map>
    </>
  );
}
