import {
  Button,
  Classes,
  Colors,
  Dialog,
  DialogBody,
  Dialog<PERSON>ooter,
  FormGroup,
  HTMLSelect,
  InputGroup,
  Switch,
  TextArea,
} from "@blueprintjs/core";
import { SearchBox } from "@mapbox/search-js-react";
import { useState } from "react";
import { useSearchParams } from "react-router-dom";
import { functions } from "../../services/functions";

export default function InternalFormSiteNew({ theme, entities }) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [location, setLocation] = useState();
  return (
    <>
      <Dialog
        style={{ width: 400 }}
        className={theme === "light" ? Classes.LIGHT : Classes.DARK}
        isOpen={searchParams.get("newSite") === "true"}
        title="New Site"
        canOutsideClickClose={false}
        onClose={() => {
          setLocation();
          setSearchParams();
        }}
      >
        <form
          onSubmit={(event) => {
            event.preventDefault();
            functions.data.sites
              .create(event, { location })
              .then(() => setSearchParams());
          }}
        >
          <DialogBody>
            <FormGroup label="Name">
              <InputGroup name="name" required />
            </FormGroup>
            <FormGroup label="Location">
              <SearchBox
                onRetrieve={(e) => setLocation(e.features[0])}
                accessToken={import.meta.env.VITE_MAPBOX_TOKEN}
                theme={{
                  variables: {
                    boxShadow:
                      "0 0 0 0 rgba(33, 93, 176, 0), 0 0 0 0 rgba(33, 93, 176, 0), inset 0 0 0 1px rgba(17, 20, 24, 0.2), inset 0 1px 1px rgba(17, 20, 24, 0.3)",
                    borderRadius: "2px",
                    border: `none`,
                    colorBackground: theme === "light" ? "white" : "black",
                    colorText: theme === "light" ? "black" : "white",
                    colorBackgroundHover:
                      theme === "light" ? "gray" : "darkgray",
                  },
                }}
              />
            </FormGroup>
            <FormGroup label="Entity">
              <HTMLSelect
                name="entity"
                required
                fill
                defaultValue={"None"}
                options={[
                  { label: "None", value: "None" },
                  ...entities.map((e) => ({
                    value: e.id,
                    label: e.name,
                  })),
                ]}
              />
            </FormGroup>
            <FormGroup label="Code">
              <InputGroup name="code" required />
            </FormGroup>
            <FormGroup label="Internal Reference">
              <InputGroup name="internalReference" required />
            </FormGroup>
            <div style={{ display: "flex", gap: 15 }}>
              <FormGroup label="Activity Sector" style={{ flex: 1 }}>
                <HTMLSelect
                  name="activitySector"
                  required
                  fill
                  defaultValue={"Logistics"}
                  options={[
                    "Logistics",
                    "Defence",
                    "Aerospace",
                    "Railway",
                    "Manufacturing",
                    "Other",
                  ]}
                />
              </FormGroup>
              <FormGroup label="Type" style={{ flex: 1 }}>
                <HTMLSelect
                  name="type"
                  required
                  fill
                  defaultValue={"Agency"}
                  options={["Agency", "Hub", "Office", "Factory", "Other"]}
                />
              </FormGroup>
            </div>
            <FormGroup>
              <Switch name="supported" label="Supported" />
            </FormGroup>
          </DialogBody>
          <DialogFooter
            actions={[
              <Button text="Cancel" key="cancel" />,
              <Button
                text="Create"
                intent="primary"
                type="submit"
                key="create"
              />,
            ]}
          />
        </form>
      </Dialog>
    </>
  );
}
