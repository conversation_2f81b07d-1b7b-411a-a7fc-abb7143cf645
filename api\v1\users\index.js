// api/v1/users/list-all.js
import dayjs from "dayjs";
import admin from "../../../lib/firebase-admin.js";

export default async function handler(req, res) {
  // AUTHORIZATION CONTROL
  if (req.headers.authorization !== `${process.env.API_KEY}`) {
    res.status(401).json({ message: "Unauthorized" });
    return;
  }

  switch (req.method) {
    case "GET":
      const users = await admin.auth().listUsers();
      res.status(200).json({ users: users.users });
      break;
    case "POST":
      const { email, password, type, role, firstName, lastName } = req.body;
      admin
        .auth()
        .createUser({
          email: email,
          password: password,
          displayName: `${firstName} ${lastName}`,
        })
        .then((user) => {
          return admin
            .firestore()
            .collection("users")
            .doc(user.uid)
            .set({
              type: type,
              role: role,
              email: email,
              firstName: firstName,
              lastName: lastName,
              createdAt: dayjs().toDate(),
              updatedAt: dayjs().toDate(),
              disabled: false,
            })
            .then(() => {
              {
                admin
                  .firestore()
                  .collection("users")
                  .doc(user.uid)
                  .collection("logs")
                  .add({
                    message: "Account created",
                    type: "auth",
                    description: `Account created on ${dayjs().format(
                      "DD/MM/YYYY HH:mm:ss"
                    )}`,
                    createdAt: dayjs().toDate(),
                  });
              }
            });
        })
        .then(() => {
          res.status(200).json({ message: "User created" });
        })
        .catch((error) => {
          res.status(500).json({ error: error.message });
        });
      break;
    case "DELETE":
      admin
        .auth()
        .updateUser(req.body.uid, {
          disabled: true,
          emailVerified: false,
        })
        .then(() => {
          admin.firestore().collection("users").doc(req.body.uid).update({
            disabled: true,
            updatedAt: dayjs().toDate(),
            deletedAt: dayjs().toDate(),
          });
        })
        .then(() => {
          res.status(200).json({ message: "User deleted" });
        })
        .catch((error) => {
          res.status(500).json({ error: error.message });
        });
      break;
    default:
      res.status(405).json({ message: "Method Not Allowed" });
      break;
  }
}
