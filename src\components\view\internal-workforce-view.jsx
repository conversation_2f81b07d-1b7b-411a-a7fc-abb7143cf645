import { But<PERSON>, <PERSON>s, Drawer, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, H5 } from "@blueprintjs/core";
import dayjs from "dayjs";
import { useSearchParams } from "react-router-dom";
import sortBy from "sort-by";

export default function InternalWorkforceView({ workforces, events }) {
  const [searchParams, setSearchParams] = useSearchParams();
  const workforce = workforces.find(
    (w) => w.id === searchParams.get("workforce")
  );
  if (!workforce) return null;

  return (
    <>
      <Drawer
        size={600}
        isOpen={searchParams.get("workforceView") === "true"}
        onClose={() => setSearchParams()}
      >
        <div
          style={{
            display: "flex",
            gap: 15,
            alignItems: "center",
            padding: 20,
          }}
        >
          <img
            height={45}
            style={{ borderRadius: "5%" }}
            src={`https://api.dicebear.com/9.x/initials/svg?seed=${workforce.firstName} ${workforce.lastName}`}
          />
          <EntityTitle
            heading={H5}
            title={`${workforce.firstName} ${workforce.lastName}`}
            subtitle={`${workforce.job} - ${workforce.activitySector}`}
            fill
            tags={
              <Button
                icon="cross"
                variant="minimal"
                onClick={() => setSearchParams()}
              />
            }
          />
        </div>
        <div>
          <div
            style={{
              padding: 20,
              borderBlock: `1px solid ${Colors.LIGHT_GRAY1}`,
              display: "flex",
              justifyContent: "space-between",
              backgroundColor: Colors.LIGHT_GRAY5,
            }}
          >
            <EntityTitle
              title={workforce.location.properties.context.place.name}
              subtitle="City"
            />
            <EntityTitle
              title={workforce.location.properties.context.country.name}
              subtitle="Country"
            />
            <EntityTitle title={workforce.mobility} subtitle="Mobility" />
            <EntityTitle
              title={workforce.activitySector}
              subtitle="Activity Sector"
            />
            <EntityTitle title={workforce.manager} subtitle="Manager" />
          </div>
          <div
            style={{
              padding: 20,
              overflow: "auto",
              scrollbarWidth: "none",
              display: "flex",
              flexDirection: "column",
              gap: 10,
            }}
          >
            {events
              .filter(
                (e) =>
                  e.ressourceId === searchParams.get("workforce") &&
                  dayjs(e.start.seconds * 1000).isAfter(dayjs())
              )
              .sort(sortBy("start"))
              .map((e) => (
                <div key={e.id}>
                  <EntityTitle
                    title={e.name}
                    subtitle={dayjs(e.start.seconds * 1000).format(
                      "DD/MM/YYYY"
                    )}
                    fill
                  />
                </div>
              ))}
          </div>
        </div>
      </Drawer>
    </>
  );
}
