import {
  <PERSON>ton,
  Entity<PERSON><PERSON>le,
  <PERSON>u,
  MenuDivider,
  MenuItem,
  Popover,
} from "@blueprintjs/core";
import { Marker } from "react-map-gl/mapbox";

export default function InternalWorkforcesMarkers({
  workforces,
  visible = true,
}) {
  // Group by city

  const workforcesByCity = workforces.reduce((acc, workforce) => {
    const city = workforce.location.properties.context.place.name;
    if (!acc[city]) {
      acc[city] = [];
    }
    acc[city].push(workforce);
    return acc;
  }, {});
  if (!visible) return null;
  return (
    <>
      {Object.keys(workforcesByCity).map((city) => (
        <Marker
          key={city}
          longitude={workforcesByCity[city][0].location.geometry.coordinates[0]}
          latitude={workforcesByCity[city][0].location.geometry.coordinates[1]}
        >
          <Popover
            placement="top-start"
            content={
              <Menu>
                <MenuDivider title={<EntityTitle title={city} />} />
                <MenuDivider />
                {workforcesByCity[city].map((workforce) => (
                  <MenuItem
                    key={workforce.id}
                    text={`${workforce.firstName} ${workforce.lastName}`}
                    label={workforce.activitySector.slice(0, 3)}
                  />
                ))}
              </Menu>
            }
          >
            <Button
              icon="people"
              size="small"
              text={workforcesByCity[city].length}
            />
          </Popover>
        </Marker>
      ))}
    </>
  );
}
