import {
  Button,
  Classes,
  Colors,
  Dialog,
  DialogBody,
  DialogFooter,
  FormGroup,
  HTMLSelect,
  InputGroup,
  TagInput,
} from "@blueprintjs/core";
import { useState } from "react";
import { useSearchParams } from "react-router-dom";
import { functions } from "../../services/functions";
import { SearchBox } from "@mapbox/search-js-react";

export default function InternalFormWorkforceNew({ theme, users }) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedMembers, setSelectedMembers] = useState([]);
  const [selectedSkills, setSelectedSkills] = useState([]);
  const [location, setLocation] = useState();
  const [loading, setLoading] = useState(false);
  return (
    <>
      <Dialog
        onClosed={() => {
          setSearchParams();
          setSelectedMembers([]);
          setSelectedSkills([]);
          setLocation();
          setLoading(false);
        }}
        style={{ width: 400 }}
        className={theme === "light" ? Classes.LIGHT : Classes.DARK}
        isOpen={searchParams.get("newWorkforce") === "true"}
        title="New Workforce"
        canOutsideClickClose={false}
        onClose={() => setSearchParams()}
      >
        <form
          onKeyDown={(event) => {
            if (event.key === "Enter") {
              event.preventDefault();
            }
          }}
          onSubmit={(event) => {
            setLoading(true);
            functions.data.workforces
              .create(event, { location, skills: selectedSkills })
              .then(() => {
                setSearchParams();
                setLoading(false);
              })
              .catch((error) => {
                console.log(error);
                setLoading(false);
              });
          }}
        >
          <DialogBody>
            <div style={{ display: "flex", gap: 15 }}>
              <FormGroup label="First Name" style={{ flex: 1 }}>
                <InputGroup name="firstName" required />
              </FormGroup>
              <FormGroup label="Last Name" style={{ flex: 1 }}>
                <InputGroup name="lastName" required />
              </FormGroup>
            </div>
            <FormGroup label="Job">
              <InputGroup name="job" required />
            </FormGroup>
            <FormGroup
              label="Location"
              className={theme === "light" ? "light" : "searchBoxInput-dark"}
            >
              <SearchBox
                onClear={() => setLocation(false)}
                value={location?.place_name}
                onRetrieve={(e) => setLocation(e.features[0])}
                accessToken={import.meta.env.VITE_MAPBOX_TOKEN}
                options={{
                  types: "place",
                }}
                theme={{
                  variables: {
                    boxShadow:
                      "0 0 0 0 rgba(33, 93, 176, 0), 0 0 0 0 rgba(33, 93, 176, 0), inset 0 0 0 1px rgba(17, 20, 24, 0.2), inset 0 1px 1px rgba(17, 20, 24, 0.3)",
                    borderRadius: "2px",
                    border: `none`,
                    colorBackground:
                      theme === "light" ? "white" : Colors.DARK_GRAY1,
                    colorText: theme === "light" ? "black" : "white",
                  },
                }}
              />
            </FormGroup>

            <div style={{ display: "flex", gap: 15 }}>
              <FormGroup label="Mobility" style={{ flex: 1 }}>
                <HTMLSelect
                  className="htmlSelectCustom"
                  name="mobility"
                  required
                  fill
                  defaultValue={"None"}
                  options={[
                    "None",
                    "Local",
                    "Regional",
                    "National",
                    "International",
                  ]}
                />
              </FormGroup>
              <FormGroup label="Activity sector" style={{ flex: 1 }}>
                <HTMLSelect
                  name="activitySector"
                  required
                  fill
                  defaultValue={"None"}
                  options={[
                    "Logistics",
                    "Defence",
                    "Aerospace",
                    "Railway",
                    "Manufacturing",
                    "Other",
                  ]}
                />
              </FormGroup>
            </div>
            <FormGroup label="Manager">
              <HTMLSelect
                name="manager"
                defaultValue={"None"}
                required
                fill
                options={[
                  ...users
                    .filter((u) => u.type === "internal")
                    .map((u) => u.email),
                  { label: "None", value: "None" },
                ]}
              />
            </FormGroup>
            <FormGroup label="Skills">
              <TagInput
                separator={","}
                name="skills"
                required
                onChange={(e) => setSelectedSkills(e)}
                values={selectedSkills}
              />
            </FormGroup>
          </DialogBody>
          <DialogFooter
            actions={[
              <Button text="Cancel" key="cancel" />,
              <Button
                text="Create"
                loading={loading}
                intent="primary"
                type="submit"
                key="create"
              />,
            ]}
          />
        </form>
      </Dialog>
    </>
  );
}
