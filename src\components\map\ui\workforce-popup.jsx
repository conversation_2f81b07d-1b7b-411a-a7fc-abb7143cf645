import { Popup } from "react-map-gl/mapbox";
import { Card, Tag, Colors } from "@blueprintjs/core";

export default function WorkforcePopup({ 
  workforce, 
  longitude, 
  latitude, 
  onClose, 
  theme 
}) {
  if (!workforce) return null;

  const getMobilityColor = (mobility) => {
    switch (mobility) {
      case "International": return Colors.RED3;
      case "National": return Colors.ORANGE3;
      case "Regional": return Colors.YELLOW3;
      case "Local": return Colors.GREEN3;
      default: return Colors.BLUE3;
    }
  };

  return (
    <Popup
      longitude={longitude}
      latitude={latitude}
      onClose={onClose}
      closeButton={true}
      closeOnClick={false}
      anchor="bottom"
      maxWidth="300px"
    >
      <Card 
        style={{ 
          margin: 0, 
          padding: 12,
          backgroundColor: theme === "light" ? "white" : Colors.DARK_GRAY1,
          color: theme === "light" ? "black" : "white"
        }}
      >
        <div style={{ marginBottom: 8 }}>
          <strong style={{ fontSize: 14 }}>{workforce.job}</strong>
        </div>
        
        <div style={{ marginBottom: 6, fontSize: 12 }}>
          📍 {workforce.city}, {workforce.country}
        </div>
        
        <div style={{ marginBottom: 8 }}>
          <Tag 
            intent="primary" 
            minimal 
            style={{ 
              backgroundColor: getMobilityColor(workforce.mobility),
              color: "white",
              fontSize: 10
            }}
          >
            {workforce.mobility}
          </Tag>
        </div>
        
        {workforce.skills && workforce.skills.length > 0 && (
          <div style={{ fontSize: 11 }}>
            <div style={{ marginBottom: 4, fontWeight: "bold" }}>Compétences:</div>
            <div style={{ display: "flex", flexWrap: "wrap", gap: 4 }}>
              {workforce.skills.slice(0, 3).map((skill, index) => (
                <Tag 
                  key={index} 
                  minimal 
                  small
                  style={{ fontSize: 9 }}
                >
                  {skill}
                </Tag>
              ))}
              {workforce.skills.length > 3 && (
                <Tag minimal small style={{ fontSize: 9 }}>
                  +{workforce.skills.length - 3}
                </Tag>
              )}
            </div>
          </div>
        )}
      </Card>
    </Popup>
  );
}
