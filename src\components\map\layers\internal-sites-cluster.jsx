import {
  <PERSON><PERSON>,
  <PERSON>s,
  <PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@blueprintjs/core";
import { useState } from "react";
import { Layer, Source } from "react-map-gl/mapbox";
import { useOutletContext, useSearchParams } from "react-router-dom";

export default function InternalSitesCluster({ sites, visible = true }) {
  const { entities } = useOutletContext();
  const [filterEntity, setFilterEntity] = useState();
  const [filterSector, setFilterSector] = useState();
  if (!visible) return null;
  return (
    <>
      <Source
        id="sites"
        type="geojson"
        data={{
          type: "FeatureCollection",
          features: sites
            .filter((s) => !filterEntity || s.entity === filterEntity)
            .filter((s) => !filterSector || s.activitySector === filterSector)
            .map((s) => ({
              type: "Feature",
              geometry: {
                type: "Point",
                coordinates: [
                  s.location.geometry.coordinates[0],
                  s.location.geometry.coordinates[1],
                ],
              },
              properties: {
                id: s.id,
                name: s.name,
              },
            })),
        }}
        cluster={true}
        clusterMaxZoom={14}
        clusterRadius={50}
      >
        {/* Layer pour les clusters */}
        <Layer
          id="clusters-sites"
          type="circle"
          source="sites"
          filter={["has", "point_count"]}
          paint={{
            "circle-color": [
              "step",
              ["get", "point_count"],
              Colors.GOLD1,
              100,
              "#f1f075",
              750,
              "#f28cb1",
            ],
            "circle-radius": [
              "step",
              ["get", "point_count"],
              20,
              100,
              30,
              750,
              40,
            ],
            "circle-stroke-width": 2,
            "circle-stroke-color": "#fff",

            "circle-opacity": 0.8,
          }}
          layout={{
            visibility: "visible",
          }}
        />

        {/* Layer pour le texte des clusters */}
        <Layer
          id="cluster-count-sites"
          type="symbol"
          source="sites"
          filter={["has", "point_count"]}
          layout={{
            "text-field": "{point_count_abbreviated}",
            "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
            "text-size": 12,
          }}
          paint={{
            "text-color": "#ffffff",
          }}
        />

        {/* Layer pour les points individuels (non clusterisés) */}
        <Layer
          id="unclustered-point-sites"
          type="circle"
          source="sites"
          filter={["!", ["has", "point_count"]]}
          paint={{
            "circle-color": Colors.GOLD1,
            "circle-radius": 6,
            "circle-stroke-width": 2,
            "circle-stroke-color": "#fff",
          }}
        />
      </Source>
      <div style={{ position: "absolute", bottom: 20, right: 20 }}>
        <Popover
          placement="top-end"
          content={
            <Menu>
              <MenuDivider title="Filter" />
              <MenuDivider />
              <MenuItem text="Entity">
                {entities.map((e) => (
                  <MenuItem
                    label={sites.filter((s) => s.entity === e.id).length}
                    onClick={() => {
                      filterEntity === e.id
                        ? setFilterEntity()
                        : setFilterEntity(e.id);
                    }}
                    active={filterEntity === e.id}
                    key={e.id}
                    text={e.name}
                    shouldDismissPopover={false}
                  />
                ))}
              </MenuItem>
            </Menu>
          }
        >
          <Button icon="filter" />
        </Popover>
      </div>
    </>
  );
}
