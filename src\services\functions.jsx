import { signInWithEmailAndPassword } from "firebase/auth";
import { auth, db } from "./firebase";
import { Colors, OverlayToaster } from "@blueprintjs/core";
import { useEffect, useMemo, useState } from "react";
import {
  addDoc,
  collection,
  doc,
  getDoc,
  onSnapshot,
  query,
  where,
} from "firebase/firestore";
import dayjs from "dayjs";
import CryptoJS from "crypto-js";

export const functions = {
  auth: {
    signin: async (event) => {
      const toaster = await OverlayToaster.create({ position: "bottom-right" });
      event.preventDefault();
      signInWithEmailAndPassword(
        auth,
        event.target.email.value,
        event.target.password.value
      )
        .then(() =>
          toaster.show({
            message: "Signed in",
            intent: "success",
            icon: "tick",
          })
        )
        .catch((error) => {
          toaster.show({
            message: error.message,
            intent: "danger",
            icon: "error",
          });
        });
    },
    User: () => {
      const [user, setUser] = useState(null);
      useEffect(() => {
        onSnapshot(doc(db, "users", auth.currentUser.uid), (doc) => {
          setUser(doc.data());
        });
      }, []);
      return user;
    },
  },
  tools: {
    toaster: async (message, intent, icon) => {
      const toaster = await OverlayToaster.create({ position: "bottom-right" });
      toaster.show({
        message: message,
        intent: intent,
        icon: icon,
      });
    },
    crypt: {
      encrypt: (text) => {
        return CryptoJS.AES.encrypt(
          text,
          import.meta.env.VITE_ENCRYPTION_KEY
        ).toString();
      },
      decrypt: (text) => {
        return CryptoJS.AES.decrypt(
          text,
          import.meta.env.VITE_ENCRYPTION_KEY
        ).toString(CryptoJS.enc.Utf8);
      },
    },
    eventColor: (type) => {
      switch (type) {
        case "Mission":
          return Colors.VIOLET3;
        case "Training":
          return Colors.BLUE3;
        case "Holiday":
          return Colors.ORANGE3;
        case "Other":
          return Colors.GREEN3;
        default:
          return "#3498db";
      }
    },
  },
  data: {
    entities: {
      InternalList: () => {
        const [entities, setEntities] = useState([]);
        useEffect(() => {
          const unsubscribe = onSnapshot(
            query(collection(db, "entities")),
            (snapOnEntities) =>
              setEntities(
                snapOnEntities.docs.map((doc) => ({
                  id: doc.id,
                  ...doc.data(),
                }))
              )
          );
          return () => unsubscribe();
        }, []);
        return entities;
      },
      create: async (event) => {
        event.preventDefault();
        const toaster = await OverlayToaster.create({
          position: "bottom-right",
        });
        addDoc(collection(db, "entities"), {
          name: event.target.name.value,
          sector: event.target.sector.value,
          description: event.target.description.value || "",
          active: true,
          members: [auth.currentUser.uid],
          owners: [auth.currentUser.uid],
          createdAt: dayjs().toDate(),
          updatedAt: dayjs().toDate(),
        })
          .then(() => {
            toaster.show({
              message: "Entity created",
              intent: "success",
              icon: "tick",
            });
          })
          .catch((error) => {
            toaster.show({
              message: error.message,
              intent: "danger",
              icon: "error",
            });
          });
      },
    },
    sites: {
      ClientList: () => {
        const [sites, setSites] = useState([]);

        useEffect(() => {
          const unsubscribe = onSnapshot(
            query(
              collection(db, "sites"),
              where("members", "array-contains", auth.currentUser.uid),
              where("active", "==", true)
            ),
            (snapOnSites) =>
              setSites(
                snapOnSites.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
              )
          );
          return () => unsubscribe();
        }, []);
        return sites;
      },
      InternalList: () => {
        const [sites, setSites] = useState([]);

        useEffect(() => {
          const unsubscribe = onSnapshot(
            query(collection(db, "sites")),
            (snapOnSites) =>
              setSites(
                snapOnSites.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
              )
          );
          return () => unsubscribe();
        }, []);
        return sites;
      },
      create: async (event, { location }) => {
        event.preventDefault();
        const toaster = await OverlayToaster.create({
          position: "bottom-right",
        });
        addDoc(collection(db, "sites"), {
          name: event.target.name.value,
          code: event.target.code.value,
          internalReference: event.target.internalReference.value,
          activitySector: event.target.activitySector.value,
          type: event.target.type.value,
          state: "Operational",
          supported: event.target.supported.checked,
          entity: event.target.entity.value,
          owners: [auth.currentUser.uid],
          members: [auth.currentUser.uid],
          active: true,
          deleted: false,
          location: location,
          createdAt: dayjs().toDate(),
          updatedAt: dayjs().toDate(),
          deletedAt: null,
        }).then((newSite) => {
          toaster.show({
            message: "Site created",
            intent: "success",
            icon: "tick",
          });
        });
      },
    },
    locations: {
      InternalList: () => {
        const [locations, setLocations] = useState([]);
        useEffect(() => {
          const unsubscribe = onSnapshot(
            query(collection(db, "locations")),
            (snapOnLocations) =>
              setLocations(
                snapOnLocations.docs.map((doc) => ({
                  id: doc.id,
                  ...doc.data(),
                }))
              )
          );
          return () => unsubscribe();
        }, []);
        return locations;
      },
    },
    workspaces: {
      InternalList: () => {
        const [workspaces, setWorkspaces] = useState([]);
        useEffect(() => {
          const unsubscribe = onSnapshot(
            query(
              collection(db, "workspaces"),
              where("members", "array-contains", auth.currentUser.uid)
            ),
            (snapOnWorkspaces) =>
              setWorkspaces(
                snapOnWorkspaces.docs.map((doc) => ({
                  id: doc.id,
                  ...doc.data(),
                }))
              )
          );
          return () => unsubscribe();
        }, []);
        return workspaces;
      },
      create: async (event, { selectedMembers }) => {
        event.preventDefault();
        const toaster = await OverlayToaster.create({
          position: "bottom-right",
        });
        addDoc(collection(db, "workspaces"), {
          name: event.target.name.value,
          description: event.target.description.value,
          data: {
            sites: [],
            entities: [],
            ressources: [],
            workfoces: [],
          },
          members: [auth.currentUser.uid, ...selectedMembers.map((m) => m.id)],
          owners: [auth.currentUser.uid],
          createdAt: dayjs().toDate(),
          updatedAt: dayjs().toDate(),
          active: true,
        }).then((newWorkspace) => {
          addDoc(collection(db, "workspaces", newWorkspace.id, "logs"), {
            message: "Workspace created",
            type: "workspace",
            description: `Workspace created on ${dayjs().format(
              "DD/MM/YYYY HH:mm:ss"
            )}`,
            user: auth.currentUser.uid,
            createdAt: dayjs().toDate(),
          });
        });
      },
    },
    workforces: {
      InternalList: () => {
        const [workforces, setWorkforces] = useState([]);
        useEffect(() => {
          const unsubscribe = onSnapshot(
            query(collection(db, "workforces")),
            (snapOnWorkforces) =>
              setWorkforces(
                snapOnWorkforces.docs.map((doc) => ({
                  id: doc.id,
                  ...doc.data(),
                }))
              )
          );
          return () => unsubscribe();
        }, []);
        return workforces;
      },
      create: async (event, { location, skills }) => {
        event.preventDefault();
        const toaster = await OverlayToaster.create({
          position: "bottom-right",
        });
        addDoc(collection(db, "workforces"), {
          firstName: event.target.firstName.value,
          lastName: event.target.lastName.value,
          job: event.target.job.value,
          activitySector: event.target.activitySector.value,
          location: location,
          mobility: event.target.mobility.value,
          skills: [...skills],
          manager: event.target.manager.value,
          active: true,
          createdAt: dayjs().toDate(),
          updatedAt: dayjs().toDate(),
        })
          .then(() =>
            toaster.show({
              message: "Workforce created",
              intent: "success",
              icon: "tick",
            })
          )
          .catch((error) => {
            toaster.show({
              message: error.message,
              intent: "danger",
              icon: "error",
            });
          });
      },
    },
    events: {
      InternalList: () => {
        const [events, setEvents] = useState([]);
        useEffect(() => {
          const unsubscribe = onSnapshot(
            query(collection(db, "events")),
            (snapOnEvents) =>
              setEvents(
                snapOnEvents.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
              )
          );
          return () => unsubscribe();
        }, []);
        return events;
      },
      create: async (event, { location, dates }) => {
        event.preventDefault();
        const toaster = await OverlayToaster.create({
          position: "bottom-right",
        });
        addDoc(collection(db, "events"), {
          name: event.target.name.value,
          description: event.target.description.value,
          start: dates[0],
          end: dates[1],
          ressourceId: event.target.workforce.value,
          type: event.target.type.value,
          location: location,
          siteId: event.target.site.value,
          creator: auth.currentUser.uid,
          active: true,
          createdAt: dayjs().toDate(),
          updatedAt: dayjs().toDate(),
        })
          .then(() =>
            toaster.show({
              message: "Event created",
              intent: "success",
              icon: "tick",
            })
          )
          .catch((error) => {
            toaster.show({
              message: error.message,
              intent: "danger",
              icon: "error",
            });
          });
      },
    },
    users: {
      internal: {
        listAll: () => {
          const [users, setUsers] = useState();
          const fireUsers = functions.data.users.internal.ListAllFromFire();

          useEffect(() => {
            // Ne faire l'appel que si firebaseUsers existe (évite les appels inutiles)
            if (fireUsers && fireUsers.length >= 0) {
              fetch(`http://127.0.0.1:3000/api/v1/users`, {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: import.meta.env.VITE_API_KEY,
                },
              })
                .then((res) => res.json())
                .then((data) => {
                  console.log("call");
                  setUsers(data.users);
                });
            }
          }, [fireUsers]);

          return users;
        },
        ListAllFromFire: () => {
          const [users, setUsers] = useState();

          useEffect(() => {
            const unsubscribe = onSnapshot(
              query(collection(db, "users")),
              (snapOnUsers) =>
                setUsers(
                  snapOnUsers.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
                )
            );
            return () => unsubscribe();
          }, []);

          return users;
        },
        create: async (event) => {
          event.preventDefault();
          const toaster = await OverlayToaster.create({
            position: "bottom-right",
          });
          fetch(`http://127.0.0.1:3000/api/v1/users`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: import.meta.env.VITE_API_KEY,
            },
            body: JSON.stringify({
              email: event.target.email.value,
              password: event.target.email.value,
              type: event.target.type.value,
              role: event.target.role.value,
              firstName: functions.tools.crypt.encrypt(
                event.target.firstName.value
              ),
              lastName: functions.tools.crypt.encrypt(
                event.target.lastName.value
              ),
            }),
          })
            .then((res) => res.json())
            .then((data) => {
              toaster.show({
                message: "User created",
                intent: "success",
                icon: "tick",
              });
            })
            .catch((error) => {
              toaster.show({
                message: error.message,
                intent: "danger",
                icon: "error",
              });
            });
        },
        delete: async (uid) => {
          const toaster = await OverlayToaster.create({
            position: "bottom-right",
          });
          fetch(`http://127.0.0.1:3000/api/v1/users`, {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
              Authorization: import.meta.env.VITE_API_KEY,
            },
            body: JSON.stringify({
              uid: uid,
            }),
          })
            .then((res) => res.json())
            .then((data) => {
              toaster.show({
                message: "User deleted",
                intent: "success",
                icon: "tick",
              });
            })
            .catch((error) => {
              toaster.show({
                message: error.message,
                intent: "danger",
                icon: "error",
              });
            });
        },
      },
    },
  },
};
