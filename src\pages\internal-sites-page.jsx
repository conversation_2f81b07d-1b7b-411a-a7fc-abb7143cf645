import { Button, Colors, HTMLTable } from "@blueprintjs/core";
import PageTitle from "../components/elements/page-title";
import { useOutletContext, useSearchParams } from "react-router-dom";
import { CSVLink } from "react-csv";

export default function InternalSitesPage() {
  const [searchParams, setSearchParams] = useSearchParams();
  const { sites, entities, theme } = useOutletContext();
  return (
    <>
      <PageTitle
        title="Sites"
        actions={
          <>
            <CSVLink
              data={sites.map((s) => ({
                code: s.code,
                name: s.name,
                location: s.location.properties.context.place.name,
                "activity sector": s.activitySector,
                type: s.type,
                entity: entities.find((e) => e.id === s.entity).name,
                supported: s.supported ? "Yes" : "No",
              }))}
            >
              <Button text="Export" icon="export" />
            </CSVLink>
            <Button
              text="Add Site"
              intent="primary"
              icon="add"
              onClick={() => setSearchParams({ newSite: "true" })}
            />
          </>
        }
      />
      <div
        style={{
          flex: 1,
          overflow: "auto",
          paddingInline: "40px 20px",
          paddingBottom: 30,
          scrollbarWidth: "none",
        }}
      >
        <HTMLTable
          width={"100%"}
          interactive
          striped
          bordered
          style={{
            border: `1px solid ${
              theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
            }`,
          }}
        >
          <thead
            style={{ position: "sticky", top: 0, backgroundColor: "white" }}
          >
            <tr>
              <th>Code</th>
              <th>Name</th>
              <th>Location</th>
              <th>Sector</th>
              <th>Type</th>
              <th>Entity</th>
              <th>Supported</th>
            </tr>
          </thead>
          <tbody>
            {sites.map((s) => (
              <tr key={s.id}>
                <td>{s.code}</td>
                <td>{s.name}</td>
                <td>{s.location.properties.context.place.name}</td>
                <td>{s.activitySector}</td>
                <td>{s.type}</td>
                <td>{entities.find((e) => e.id === s.entity).name}</td>
                <td>{s.supported ? "Yes" : "No"}</td>
              </tr>
            ))}
          </tbody>
        </HTMLTable>
      </div>
    </>
  );
}
