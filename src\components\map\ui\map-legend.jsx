import { Card, Colors } from "@blueprintjs/core";

export default function MapLegend({ theme }) {
  const legendItems = [
    { label: "International", color: "#e74c3c" },
    { label: "National", color: "#f39c12" },
    { label: "Regional", color: "#f1c40f" },
    { label: "Local", color: "#2ecc71" },
    { label: "None", color: "#3498db" }
  ];

  return (
    <Card
      style={{
        position: "absolute",
        bottom: 20,
        left: 20,
        padding: 12,
        backgroundColor: theme === "light" ? "white" : Colors.DARK_GRAY1,
        color: theme === "light" ? "black" : "white",
        minWidth: 150,
        boxShadow: theme === "light" 
          ? "0 2px 8px rgba(0,0,0,0.15)" 
          : "0 2px 8px rgba(0,0,0,0.3)"
      }}
    >
      <div style={{ 
        fontSize: 12, 
        fontWeight: "bold", 
        marginBottom: 8,
        borderBottom: `1px solid ${theme === "light" ? Colors.LIGHT_GRAY3 : Colors.DARK_GRAY3}`,
        paddingBottom: 4
      }}>
        Mobilité
      </div>
      
      {legendItems.map((item, index) => (
        <div 
          key={index}
          style={{ 
            display: "flex", 
            alignItems: "center", 
            marginBottom: 4,
            fontSize: 11
          }}
        >
          <div
            style={{
              width: 12,
              height: 12,
              borderRadius: "50%",
              backgroundColor: item.color,
              marginRight: 8,
              border: "2px solid white",
              boxShadow: "0 1px 2px rgba(0,0,0,0.2)"
            }}
          />
          <span>{item.label}</span>
        </div>
      ))}
      
      <div style={{ 
        marginTop: 8, 
        paddingTop: 8,
        borderTop: `1px solid ${theme === "light" ? Colors.LIGHT_GRAY3 : Colors.DARK_GRAY3}`,
        fontSize: 10,
        color: theme === "light" ? Colors.GRAY1 : Colors.GRAY4
      }}>
        <div style={{ display: "flex", alignItems: "center", marginBottom: 4 }}>
          <div
            style={{
              width: 20,
              height: 20,
              borderRadius: "50%",
              backgroundColor: "#51bbd6",
              marginRight: 8,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              fontSize: 8,
              fontWeight: "bold"
            }}
          >
            N
          </div>
          <span>Cluster (N = nombre)</span>
        </div>
      </div>
    </Card>
  );
}
