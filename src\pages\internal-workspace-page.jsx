import { useOutletContext, useParams } from "react-router-dom";
import PageTitle from "../components/elements/page-title";
import { Colors, NonIdealState, Tab, TabPanel, Tabs } from "@blueprintjs/core";
import { useState } from "react";
import { auth } from "../services/firebase";
import InternalWorkspaceOverview from "../components/workspaces/internal-workspace-overview";
import InternalWorkspaceSites from "../components/workspaces/internal-workspace-sites";

export default function InternalWorkspacePage() {
  const { workspaces, theme, user, sites } = useOutletContext();
  const [selectedTab, setSelectedTab] = useState("overview");
  const { workspaceId } = useParams();

  const workspace = workspaces.find((w) => w.id === workspaceId);

  if (!workspace)
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          flex: 1,
          alignItems: "center",
        }}
      >
        <NonIdealState
          icon="root-folder"
          title="Workspace not found"
          description="The workspace you are looking for does not exist. Or you don't have access to it."
        />
      </div>
    );

  return (
    <>
      <PageTitle
        title={
          <div style={{ display: "flex", gap: 15, alignItems: "center" }}>
            <img
              src={`https://api.dicebear.com/9.x/identicon/svg?seed=${workspace.name}&radius=5&rowColor=af007c&backgroundColor=ffffff`}
              height={30}
            />
            {workspace.name}
          </div>
        }
      />
      <div
        style={{
          display: "flex",
          flex: 1,
          borderTop: `1px solid ${
            theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
          }`,
        }}
      >
        <div
          style={{
            paddingInline: "40px 20px",
            paddingBlock: "20px 0px",
            borderRight: `1px solid ${
              theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
            }`,
          }}
        >
          <Tabs
            fill
            selectedTabId={selectedTab}
            onChange={(tab) => setSelectedTab(tab)}
            vertical
          >
            <Tab title="Overview" id={"overview"} />
            <Tab title="Sites" id={"sites"} />
            <Tab title="Entities" id={"entities"} />
            <Tab title="Ressources" id={"ressources"} />
            <Tab title="Workfoces" id={"workfoces"} />
            <Tab title="Issues" id={"issues"} />
            <Tab title="Missions" id={"missions"} />
            <Tab title="Activities" id={"activities"} />
            <Tab title="Planning" id={"planning"} />
          </Tabs>
        </div>
        <div
          style={{
            paddingInline: "40px 20px",
            flex: 1,
            overflow: "hidden",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <TabPanel
            id={"overview"}
            panel={
              <InternalWorkspaceOverview
                workspace={workspaces.find((w) => w.id === workspaceId)}
              />
            }
            selectedTabId={selectedTab}
          />
          <TabPanel
            id={"sites"}
            panel={
              <InternalWorkspaceSites
                workspace={workspaces.find((w) => w.id === workspaceId)}
                sites={sites.filter((s) => workspace.data.sites.includes(s.id))}
              />
            }
            selectedTabId={selectedTab}
          />
          <TabPanel
            id={"entities"}
            panel={"Entities"}
            selectedTabId={selectedTab}
          />
          <TabPanel
            id={"ressources"}
            panel={"Ressources"}
            selectedTabId={selectedTab}
          />
          <TabPanel
            id={"workfoces"}
            panel={"Workfoces"}
            selectedTabId={selectedTab}
          />
          <TabPanel
            id={"issues"}
            panel={"Issues"}
            selectedTabId={selectedTab}
          />
          <TabPanel
            id={"missions"}
            panel={"Missions"}
            selectedTabId={selectedTab}
          />
        </div>
      </div>
    </>
  );
}
