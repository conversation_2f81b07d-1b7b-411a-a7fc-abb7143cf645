import {
  <PERSON><PERSON>,
  Entity<PERSON><PERSON>le,
  H5,
  InputGroup,
  TextArea,
} from "@blueprintjs/core";
import { addDoc, collection, onSnapshot, query } from "firebase/firestore";
import { useEffect, useState } from "react";
import { auth, db } from "../../services/firebase";
import dayjs from "dayjs";
import sortBy from "sort-by";

export default function InternalWorkspaceOverview({ workspace }) {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState("");

  useEffect(() => {
    onSnapshot(
      query(collection(db, "workspaces", workspace.id, "messages")),
      (snapOnMessages) =>
        setMessages(
          snapOnMessages.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
        )
    );
  }, []);
  return (
    <>
      <div style={{ paddingBottom: 10 }}>
        <EntityTitle
          heading={H5}
          title={"Overview"}
          subtitle={workspace.name}
        />
      </div>
      <div style={{ flex: 1, overflow: "hidden", display: "flex" }}>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            width: "30%",
          }}
        >
          <div
            style={{
              flex: 1,
              overflow: "auto",
              display: "flex",
              flexDirection: "column",
              gap: 10,
            }}
          >
            {messages.sort(sortBy("createdAt")).map((m) => (
              <div key={m.id}>
                <EntityTitle
                  title={m.content}
                  subtitle={dayjs(m.createdAt.seconds * 1000).format(
                    "DD/MM/YYYY HH:mm:ss"
                  )}
                />
              </div>
            ))}
          </div>
          <div>
            <TextArea
              fill
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
            />
            <Button
              text="Send"
              intent="primary"
              fill
              onClick={() =>
                addDoc(collection(db, "workspaces", workspace.id, "messages"), {
                  content: newMessage,
                  user: auth.currentUser.uid,
                  createdAt: new Date(),
                }).then(() => setNewMessage(""))
              }
            />
          </div>
        </div>
      </div>
    </>
  );
}
