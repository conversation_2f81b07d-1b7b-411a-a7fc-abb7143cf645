import { Marker } from "react-map-gl/mapbox";
import CustomMarker from "../ui/custom-marker";
import { EntityTitle, <PERSON>u, MenuDivider, Tag } from "@blueprintjs/core";

export default function InternalSitesMarkers({
  sites,
  visible = true,
  theme,
  entities,
}) {
  if (!visible) return null;
  return (
    <>
      {sites.map((site) => (
        <CustomMarker
          theme={theme}
          key={site.id}
          latitude={site.location.geometry.coordinates[1]}
          longitude={site.location.geometry.coordinates[0]}
          popContent={
            <Menu style={{ width: 300 }}>
              <MenuDivider
                title={
                  <EntityTitle
                    fill
                    title={site.name}
                    subtitle={entities.find((e) => e.id === site.entity).name}
                    tags={<Tag minimal>{site.activitySector}</Tag>}
                  />
                }
              />
              <MenuDivider />
            </Menu>
          }
        />
      ))}
    </>
  );
}
