import { Button, Colors } from "@blueprintjs/core";
import dayjs from "dayjs";
import { useState } from "react";
import { Outlet } from "react-router-dom";
import Logo from "../assets/logo";
import { functions } from "../../services/functions";
import GlobalLoader from "../elements/global-loader";

export default function GlobalLayout() {
  const [theme, setTheme] = useState("light");
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const user = functions.auth.User();

  if (!user) return <GlobalLoader />;
  return (
    <div
      className={theme === "light" ? null : "bp6-dark"}
      style={{
        display: "flex",
        flexDirection: "column",
        minHeight: "100svh",
        maxHeight: "100svh",
        overflow: "hidden",
        backgroundColor: theme === "light" ? "white" : Colors.DARK_GRAY1,
      }}
    >
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <div
          style={{
            height: 50,
            width: sidebarOpen ? 230 : 46,
            display: "flex",
            alignItems: "center",
            paddingInline: sidebarOpen ? 15 : 0,
            borderRight: `1px solid ${
              theme === "light"
                ? Colors.LIGHT_GRAY1
                : "rgba(255, 255, 255, 0.2)"
            }`,
            justifyContent: sidebarOpen ? "space-between" : "center",
          }}
        >
          {sidebarOpen && <Logo />}
          <Button
            size="small"
            variant="minimal"
            icon={sidebarOpen ? "menu-closed" : "menu-open"}
            onClick={() => setSidebarOpen(!sidebarOpen)}
          />
        </div>
        <div
          style={{
            display: "flex",
            flex: 1,
            justifyContent: "space-between",
            alignItems: "center",
            paddingInline: "40px 15px",
            height: 50,
          }}
        >
          <div>{dayjs().format("dddd DD MMMM YYYY")}</div>
          <div style={{ display: "flex", gap: 0 }}>
            <Button
              variant="minimal"
              icon={theme === "light" ? "moon" : "flash"}
              onClick={() => setTheme(theme === "light" ? "dark" : "light")}
            />
            <Button icon="cog" variant="minimal" />
            <Button icon="notifications" variant="minimal" />
            <Button icon="user" variant="minimal" />
          </div>
        </div>
      </div>
      <div style={{ flex: 1, overflow: "hidden", display: "flex" }}>
        <Outlet context={{ theme, sidebarOpen, user }} />
      </div>
    </div>
  );
}
