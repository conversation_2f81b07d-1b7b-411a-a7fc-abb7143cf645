{"name": "portal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@blueprintjs/core": "^6.1.0", "@blueprintjs/datetime": "^6.0.1", "@blueprintjs/select": "^6.0.1", "@fullcalendar/core": "^6.1.19", "@fullcalendar/daygrid": "^6.1.19", "@fullcalendar/interaction": "^6.1.19", "@fullcalendar/react": "^6.1.19", "@fullcalendar/resource": "^6.1.19", "@fullcalendar/resource-timeline": "^6.1.19", "@mapbox/search-js-react": "^1.3.0", "@types/mapbox-gl": "^3.4.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "firebase": "^12.0.0", "firebase-admin": "^13.4.0", "mapbox-gl": "^3.14.0", "react": "^18.0.0", "react-csv": "^2.2.2", "react-dom": "^18.0.0", "react-map-gl": "^8.0.4", "react-router-dom": "^7.7.1", "sort-by": "^1.2.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}}