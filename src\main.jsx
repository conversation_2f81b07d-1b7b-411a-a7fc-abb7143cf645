import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import GlobalContext from "./context/global-context.jsx";
import App from "./App.jsx";

import "normalize.css";
import "@blueprintjs/core/lib/css/blueprint.css";
// include blueprint-icons.css for icon font support
import "@blueprintjs/icons/lib/css/blueprint-icons.css";
import "@blueprintjs/select/lib/css/blueprint-select.css";
import "@blueprintjs/datetime/lib/css/blueprint-datetime.css";
import "./custom.css";
import "mapbox-gl/dist/mapbox-gl.css";

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <GlobalContext>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </GlobalContext>
  </StrictMode>
);
