import { Colors, Popover } from "@blueprintjs/core";
import { Marker } from "react-map-gl/mapbox";

export default function CustomMarker({
  theme,
  longitude,
  latitude,
  borderColor = Colors.BLUE3,
  bgColor = theme === "light" ? "white" : Colors.BLACK,
  iconColor = Colors.BLUE3,
  popContent,
  popPlacement = "top-start",
}) {
  return (
    <Marker longitude={longitude} latitude={latitude} anchor="bottom">
      <Popover content={popContent} placement={popPlacement}>
        <svg
          cursor={"pointer"}
          width={20}
          viewBox="0 0 25 29"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <mask id="path-1-inside-1_10_49" fill="white">
            <path d="M23 0C24.1046 0 25 0.895431 25 2V23C25 24.1046 24.1046 25 23 25H16.415L13.8301 27.585C13.049 28.3658 11.7829 28.3659 11.002 27.585L8.41699 25H2C0.895431 25 0 24.1046 0 23V2C0 0.895431 0.895431 0 2 0H23Z" />
          </mask>
          <path
            d="M23 0C24.1046 0 25 0.895431 25 2V23C25 24.1046 24.1046 25 23 25H16.415L13.8301 27.585C13.049 28.3658 11.7829 28.3659 11.002 27.585L8.41699 25H2C0.895431 25 0 24.1046 0 23V2C0 0.895431 0.895431 0 2 0H23Z"
            fill={bgColor}
          />
          <path
            d="M16.415 25V24H16.0008L15.7079 24.2929L16.415 25ZM13.8301 27.585L14.5371 28.2922L14.5372 28.2921L13.8301 27.585ZM11.002 27.585L11.7091 26.8779L11.002 27.585ZM8.41699 25L9.1241 24.2929L8.83121 24H8.41699V25ZM23 0V1C23.5523 1 24 1.44772 24 2H25H26C26 0.343146 24.6569 -1 23 -1V0ZM25 2H24V23H25H26V2H25ZM25 23H24C24 23.5523 23.5523 24 23 24V25V26C24.6569 26 26 24.6569 26 23H25ZM23 25V24H16.415V25V26H23V25ZM16.415 25L15.7079 24.2929L13.123 26.8779L13.8301 27.585L14.5372 28.2921L17.1221 25.7071L16.415 25ZM13.8301 27.585L13.1231 26.8778C12.7324 27.2683 12.0994 27.2682 11.7091 26.8779L11.002 27.585L10.2948 28.2921C11.4664 29.4637 13.3656 29.4633 14.5371 28.2922L13.8301 27.585ZM11.002 27.585L11.7091 26.8779L9.1241 24.2929L8.41699 25L7.70989 25.7071L10.2948 28.2921L11.002 27.585ZM8.41699 25V24H2V25V26H8.41699V25ZM2 25V24C1.44772 24 1 23.5523 1 23H0H-1C-1 24.6569 0.343146 26 2 26V25ZM0 23H1V2H0H-1V23H0ZM0 2H1C1 1.44772 1.44772 1 2 1V0V-1C0.343146 -1 -1 0.343146 -1 2H0ZM2 0V1H23V0V-1H2V0Z"
            fill={borderColor}
            mask="url(#path-1-inside-1_10_49)"
          />
          <path
            d="M12.5 6C9.9845 6 7.95 7.937 7.95 10.3355C7.95 12.7275 12.5 19 12.5 19C12.5 19 17.05 12.7275 17.05 10.3355C17.05 7.9435 15.009 6 12.5 6ZM12.5 12.5C11.421 12.5 10.55 11.629 10.55 10.55C10.55 9.471 11.421 8.6 12.5 8.6C13.579 8.6 14.45 9.471 14.45 10.55C14.45 11.629 13.579 12.5 12.5 12.5Z"
            fill={iconColor}
          />
        </svg>
      </Popover>
    </Marker>
  );
}
