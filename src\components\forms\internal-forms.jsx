import InternalFormCustomerNew from "./internal-form-customer-new";
import InternalFormEntityNew from "./internal-form-entity-new";
import InternalFormWorkforceNew from "./internal-form-workforce-new";
import InternalFormSiteNew from "./intertnal-form-site-new";

export default function InternalForms({
  theme,
  sites,
  entities,
  ressources,
  workfoces,
  users,
}) {
  return (
    <>
      <InternalFormWorkforceNew theme={theme} users={users} />
      <InternalFormCustomerNew theme={theme} />
      <InternalFormEntityNew theme={theme} />
      <InternalFormSiteNew theme={theme} entities={entities} />
    </>
  );
}
