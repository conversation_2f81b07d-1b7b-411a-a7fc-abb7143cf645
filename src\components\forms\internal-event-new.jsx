import {
  Button,
  <PERSON><PERSON>,
  DialogBody,
  DialogFooter,
  FormGroup,
  HTMLSelect,
  InputGroup,
  TextArea,
} from "@blueprintjs/core";
import { DateRangeInput } from "@blueprintjs/datetime";
import { SearchBox } from "@mapbox/search-js-react";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { useOutletContext } from "react-router-dom";
import { functions } from "../../services/functions";

export default function InternalEventNew({ infos = null }) {
  const [open, setOpen] = useState(false);
  const [data, setData] = useState(infos);
  const [dates, setDates] = useState([null, null]);
  const [loading, setLoading] = useState(false);
  const [location, setLocation] = useState();
  const { workforces, sites } = useOutletContext();

  useEffect(() => {
    setData(infos);
    setDates([infos?.start, dayjs(infos?.end).subtract(1, "minutes").toDate()]);
  }, [infos]);

  return (
    <>
      <Button
        disabled={!data}
        text="New Event"
        intent="primary"
        icon="add"
        onClick={() => setOpen(true)}
      />
      <Dialog
        onClosed={() => {
          setData(null);
          setOpen(false);
          setLocation(null);
          setDates([null, null]);
          setLoading(false);
        }}
        style={{ width: 400 }}
        isOpen={open}
        title="New Event"
        canOutsideClickClose={false}
        onClose={() => setOpen(false)}
      >
        <form
          onKeyDown={(event) => {
            if (event.key === "Enter") {
              event.preventDefault();
            }
          }}
          onSubmit={(event) => {
            functions.data.events
              .create(event, { location, dates })
              .then(() => setOpen(false));
          }}
        >
          <DialogBody>
            <FormGroup label="Name">
              <InputGroup name="name" required />
            </FormGroup>
            <FormGroup label="Workforce">
              <HTMLSelect
                name="workforce"
                required
                fill
                options={workforces.map((w) => ({
                  value: w.id,
                  label: `${w.firstName} ${w.lastName}`,
                }))}
                defaultValue={data?.resource?.id}
              />
            </FormGroup>
            <FormGroup label="Date">
              <DateRangeInput
                name="date"
                onChange={(e) => setDates(e)}
                required
                fill
                formatDate={(date) => dayjs(date).format("DD/MM/YYYY")}
                shortcuts={false}
                defaultValue={[
                  data?.start,
                  dayjs(data?.end).subtract(1, "minutes").toDate(),
                ]}
              />
            </FormGroup>
            <FormGroup label="Location" className="searchBoxInput-light">
              <SearchBox
                value={location?.place_name}
                onRetrieve={(e) => setLocation(e.features[0])}
                name="location"
                theme={{
                  variables: {
                    boxShadow:
                      "0 0 0 0 rgba(33, 93, 176, 0), 0 0 0 0 rgba(33, 93, 176, 0), inset 0 0 0 1px rgba(17, 20, 24, 0.2), inset 0 1px 1px rgba(17, 20, 24, 0.3)",
                    borderRadius: "2px",
                    border: `none`,
                    colorBackground: "white",
                    colorText: "black",
                    colorBackgroundHover: "white",
                  },
                }}
                required
                accessToken={import.meta.env.VITE_MAPBOX_TOKEN}
              />
            </FormGroup>
            <div style={{ display: "flex", gap: 15 }}>
              <FormGroup label="Event Type" style={{ flex: 1 }}>
                <HTMLSelect
                  defaultValue={"Mission"}
                  name="type"
                  required
                  fill
                  options={["Mission", "Training", "Holiday", "Other"]}
                />
              </FormGroup>
              <FormGroup label="Site" style={{ flex: 1 }}>
                <HTMLSelect
                  name="site"
                  required
                  defaultValue={"None"}
                  fill
                  options={[
                    { label: "None", value: "None" },
                    ...sites.map((s) => ({
                      value: s.id,
                      label: s.name,
                    })),
                  ]}
                />
              </FormGroup>
            </div>
            <FormGroup label="Description">
              <TextArea fill name="description" />
            </FormGroup>
          </DialogBody>
          <DialogFooter
            actions={[
              <Button text="Cancel" key="cancel" />,
              <Button
                text="Create"
                intent="primary"
                type="submit"
                key="create"
              />,
            ]}
          />
        </form>
      </Dialog>
    </>
  );
}
