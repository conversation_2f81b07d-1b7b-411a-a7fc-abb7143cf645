# Améliorations de la Heatmap des Sites

## Vue d'ensemble

La heatmap des sites a été retravaillée pour créer une carte de chaleur plus cohérente basée sur la densité réelle des sites à proximité. Cette amélioration permet une meilleure visualisation des zones de concentration des sites.

## Principales améliorations

### 1. Calcul de densité intelligente

- **Algorithme de proximité** : Chaque site calcule le nombre de sites voisins dans un rayon de 50km
- **Poids dynamique** : Le poids de chaque point est calculé en fonction de sa densité locale
- **Formule** : `poids = 1 + min(nombre_voisins * 0.5, 5)`

### 2. Propriétés visuelles optimisées

#### Rayon adaptatif
```javascript
"heatmap-radius": [
  "interpolate", ["linear"], ["zoom"],
  0, 15,   // Petit rayon aux faibles zooms
  5, 25,   // Rayon moyen
  10, 40,  // Rayon élevé
  15, 60   // Rayon maximum pour les détails
]
```

#### Poids basé sur la densité
```javascript
"heatmap-weight": [
  "interpolate", ["linear"], ["get", "weight"],
  1, 0.5,  // Sites isolés
  3, 1.5,  // Sites avec quelques voisins
  6, 3     // Zones très denses
]
```

#### Gradient de couleur amélioré
- **Transparent** → **Bleu léger** → **Orange** → **Rouge intense**
- Meilleure lisibilité et contraste
- Progression logique de la densité

### 3. Performance optimisée

- **useMemo** : Calcul de la densité mis en cache
- **Recalcul intelligent** : Seulement quand la liste des sites change
- **ID de source unique** : Évite les conflits avec d'autres couches

## Utilisation

La heatmap s'utilise de la même manière qu'avant :

```jsx
<InternalSitesHeatmap sites={sites} visible={true} />
```

### Propriétés

- `sites` : Array des sites avec coordonnées géographiques
- `visible` : Boolean pour afficher/masquer la heatmap

## Algorithme de calcul de distance

Utilise la formule de Haversine pour calculer la distance entre deux points géographiques :

```javascript
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Rayon de la Terre en km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};
```

## Paramètres configurables

- **nearbyRadius** : Rayon de recherche des sites voisins (50km par défaut)
- **densityMultiplier** : Facteur de multiplication de la densité (0.5 par défaut)
- **maxWeight** : Poids maximum autorisé (5 par défaut)

## Résultats attendus

1. **Zones denses** : Apparaissent en rouge/orange intense
2. **Zones moyennement denses** : Apparaissent en orange/bleu
3. **Sites isolés** : Apparaissent en bleu léger ou restent transparents
4. **Adaptation au zoom** : Plus de détails aux zooms élevés
5. **Performance** : Calculs optimisés pour de grandes quantités de données

## Compatibilité

- Compatible avec tous les autres composants de carte existants
- Pas de conflit d'ID de source
- Fonctionne avec les thèmes clair/sombre
- Responsive selon le niveau de zoom
