import { useOutletContext, useSearchParams } from "react-router-dom";
import PageTitle from "../components/elements/page-title";
import { Button, Card, EntityTitle, H4, H5, Tag } from "@blueprintjs/core";

export default function InternalEntitiesPage() {
  const [searchParams, setSearchParams] = useSearchParams();
  const { entities, sites } = useOutletContext();
  return (
    <>
      <PageTitle
        title="Entities"
        actions={
          <>
            <Button text="Export" icon="export" />
            <Button
              text="Add Entity"
              intent="primary"
              icon="add"
              onClick={() => setSearchParams({ newEntity: "true" })}
            />
          </>
        }
      />
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
          gap: 15,
          paddingInline: "40px 20px",
        }}
      >
        {entities.map((e) => (
          <Card
            key={e.id}
            compact
            interactive
            style={{
              minWidth: 300,
            }}
          >
            <EntityTitle
              title={e.name}
              heading={H5}
              fill
              subtitle={e.sector}
              tags={<Tag>{e.active ? "Active" : "Inactive"}</Tag>}
            />
            <div style={{ display: "flex", gap: 15, paddingTop: 10 }}>
              <EntityTitle
                title={sites.filter((s) => s.entity === e.id).length}
                subtitle="Sites"
              />
              <EntityTitle title={e.members.length} subtitle="Members" />
            </div>
          </Card>
        ))}
      </div>
    </>
  );
}
