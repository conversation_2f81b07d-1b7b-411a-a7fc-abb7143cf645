import { Layer, Source } from "react-map-gl/mapbox";

export default function InternalWorkforcesCluster({
  workforces,
  visible = true,
}) {
  if (!visible) return null;
  return (
    <>
      <Source
        id="workforces"
        type="geojson"
        data={{
          type: "FeatureCollection",
          features: workforces.map((w) => ({
            type: "Feature",
            geometry: {
              type: "Point",
              coordinates: [
                w.location.geometry.coordinates[0],
                w.location.geometry.coordinates[1],
              ],
            },
            properties: {
              id: w.id,
              job: w.job,
              city: w.location.properties.context.place.name,
              country: w.location.properties.context.country.name,
              mobility: w.mobility,
            },
          })),
        }}
        cluster={true}
        clusterMaxZoom={14}
        clusterRadius={50}
      >
        {/* Layer pour les clusters */}
        <Layer
          id="clusters"
          type="circle"
          source="workforces"
          filter={["has", "point_count"]}
          paint={{
            "circle-color": [
              "step",
              ["get", "point_count"],
              "#af007c",
              100,
              "#f1f075",
              750,
              "#f28cb1",
            ],
            "circle-radius": [
              "step",
              ["get", "point_count"],
              20,
              100,
              30,
              750,
              40,
            ],
            "circle-stroke-width": 2,
            "circle-stroke-color": "#fff",

            "circle-opacity": 0.8,
          }}
          layout={{
            visibility: "visible",
          }}
        />

        {/* Layer pour le texte des clusters */}
        <Layer
          id="cluster-count"
          type="symbol"
          source="workforces"
          filter={["has", "point_count"]}
          layout={{
            "text-field": "{point_count_abbreviated}",
            "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
            "text-size": 12,
          }}
          paint={{
            "text-color": "#ffffff",
          }}
        />

        {/* Layer pour les points individuels (non clusterisés) */}
        <Layer
          id="unclustered-point"
          type="circle"
          source="workforces"
          filter={["!", ["has", "point_count"]]}
          paint={{
            "circle-color": "#af007c",
            "circle-radius": 6,
            "circle-stroke-width": 2,
            "circle-stroke-color": "#fff",
          }}
        />
      </Source>
    </>
  );
}
