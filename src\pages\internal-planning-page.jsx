import FullCalendar from "@fullcalendar/react";
import PageTitle from "../components/elements/page-title";
import resourceTimelinePlugin from "@fullcalendar/resource-timeline";
import interactionPlugin from "@fullcalendar/interaction";
import { useOutletContext } from "react-router-dom";
import dayjs from "dayjs";
import sortBy from "sort-by";
import { Button, ButtonGroup } from "@blueprintjs/core";
import { useEffect, useRef, useState } from "react";
import InternalEventNew from "../components/forms/internal-event-new";
import { functions } from "../services/functions";

export default function InternalPlanningPage() {
  const [firstDate, setFirstDate] = useState(dayjs().startOf("year"));
  const { workforces, events } = useOutletContext();
  const planningRef = useRef(null);
  const [selected, setSelected] = useState(null);

  useEffect(() => {
    planningRef.current?.getApi().gotoDate(firstDate.toDate());
  }, [firstDate]);

  return (
    <>
      <PageTitle
        title="Planning"
        actions={
          <>
            <ButtonGroup>
              <Button
                icon="caret-left"
                onClick={() => setFirstDate(firstDate.subtract(1, "week"))}
              />
              <Button
                text="Today"
                onClick={() => setFirstDate(dayjs().startOf("week"))}
              />
              <Button
                icon="caret-right"
                onClick={() => setFirstDate(firstDate.add(1, "week"))}
              />
            </ButtonGroup>
            <InternalEventNew infos={selected} />
          </>
        }
      />
      <div
        style={{
          flex: 1,
          paddingInline: "40px 20px",
          paddingBottom: 30,
          overflow: "auto",
        }}
      >
        <FullCalendar
          events={events.map((e) => ({
            id: e.id,
            title: `${e.name} (${e.type})`,
            start: e.start.toDate(),
            end: dayjs(e.end.seconds * 1000)
              .add(1, "minutes")
              .toDate(),
            resourceId: e.ressourceId,
            allDay: true,
            color: functions.tools.eventColor(e.type),
          }))}
          select={(info) => setSelected(info)}
          ref={planningRef}
          schedulerLicenseKey="CC-Attribution-NonCommercial-NoDerivatives"
          height={"100%"}
          selectable
          editable
          headerToolbar={null}
          plugins={[resourceTimelinePlugin, interactionPlugin]}
          initialView="resourceTimeline"
          resources={workforces.sort(sortBy("lastName")).map((w) => ({
            id: w.id,
            title: `${w.firstName} ${w.lastName}`,
            activitySector: w.activitySector,
            location: w.location.properties.context.place.name,
            country: w.location.properties.context.country.name,
          }))}
          duration={{ days: 365 }}
          nowIndicator
          slotDuration={{ days: 1 }}
          businessHours
          initialDate={firstDate.format("YYYY-MM-DD")}
          firstDay={1}
          resourceGroupField={["activitySector"]}
          resourceAreaColumns={[
            { field: "title", headerContent: "Workforce" },
            { field: "location", headerContent: "City" },
          ]}
          slotLabelFormat={[
            { month: "long", year: "numeric" },
            { week: "short" },
            { day: "numeric" },
          ]}
        />
      </div>
    </>
  );
}
