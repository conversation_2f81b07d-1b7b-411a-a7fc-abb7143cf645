import { Colors, NonIdealState } from "@blueprintjs/core";

export default function DataUpload({ theme }) {
  return (
    <div
      style={{
        flex: 1,
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        borderTop: `1px solid ${
          theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
        }`,
      }}
    >
      <NonIdealState
        icon="data-connection"
        title="Data loading..."
        description="Please wait while we load your data."
      />
    </div>
  );
}
