import { Button, Colors, EntityTitle, H5, HTMLTable } from "@blueprintjs/core";
import { useSearchParams } from "react-router-dom";

export default function InternalWorkspaceSites({ workspace, sites }) {
  const [searchParams, setSearchParams] = useSearchParams();
  return (
    <>
      <EntityTitle
        heading={H5}
        title={"Sites"}
        subtitle={workspace.name}
        fill
        tags={<Button text="Add Site" intent="primary" icon="add" />}
      />
      <div
        style={{
          paddingBlock: 10,
          height: "100%",
        }}
      >
        <HTMLTable
          width={"100%"}
          style={{
            border: `1px solid ${Colors.LIGHT_GRAY1}`,
          }}
        >
          <thead>
            <tr>
              <th>Name</th>
              <th>Location</th>
              <th>Members</th>
            </tr>
          </thead>
        </HTMLTable>
      </div>
    </>
  );
}
