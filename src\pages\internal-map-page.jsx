import { useRef, useState } from "react";
import InternalMap from "../components/map/internal-map";
import { useOutletContext } from "react-router-dom";
import { Colors } from "@blueprintjs/core";
import InternalRightSidebar from "../components/map/internal-right-sidebar";

export default function InternalMapPage() {
  const mapRef = useRef(null);
  const [center, setCenter] = useState(null);
  const { theme, sidebarOpen } = useOutletContext();
  const [display, setDisplay] = useState(["sitesMarkers"]);

  return (
    <>
      <div
        style={{
          display: "flex",
          flex: 1,
          borderTop: `1px solid ${
            theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
          }`,
        }}
      >
        <div
          style={{
            flex: 1,
          }}
        >
          <InternalMap
            mapRef={mapRef}
            theme={theme}
            setCenter={setCenter}
            sidebarOpen={sidebarOpen}
            display={display}
          />
        </div>
        <div
          style={{
            width: 230,
            display: "flex",
            borderLeft: `1px solid ${
              theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
            }`,
            backgroundColor:
              theme === "light" ? Colors.LIGHT_GRAY5 : Colors.DARK_GRAY1,
          }}
        >
          <InternalRightSidebar
            mapRef={mapRef}
            theme={theme}
            display={display}
            setDisplay={setDisplay}
          />
        </div>
      </div>
    </>
  );
}
