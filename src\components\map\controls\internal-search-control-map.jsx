import { Colors } from "@blueprintjs/core";
import { SearchBox } from "@mapbox/search-js-react";
import { useState } from "react";
import { Marker } from "react-map-gl/mapbox";
import CustomMarker from "../ui/custom-marker";

export default function InternalSearchControlMap({
  theme,
  mapRef,
  visible = true,
}) {
  const [retrieve, setRetrieve] = useState(false);
  if (!visible) return null;
  return (
    <>
      <div
        style={{ position: "absolute", top: 20, left: 40 }}
        className={
          theme === "light" ? "searchBoxInput-light" : "searchBoxInput-dark"
        }
      >
        <SearchBox
          onClear={() => setRetrieve(false)}
          onRetrieve={(e) => {
            setRetrieve(e.features[0]);
            mapRef.current?.flyTo({
              center: [
                e.features[0].geometry.coordinates[0],
                e.features[0].geometry.coordinates[1],
              ],
              speed: 1,
              zoom: 10,
            });
          }}
          options={{
            proximity: mapRef?.current?.getCenter(),
          }}
          accessToken={import.meta.env.VITE_MAPBOX_TOKEN}
          theme={{
            variables: {
              boxShadow: "none",
              borderRadius: "2px",
              border: `1px solid ${
                theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
              }`,
              colorBackground: theme === "light" ? "white" : Colors.DARK_GRAY1,
              colorText: theme === "light" ? "black" : "white",
              colorBackgroundHover:
                theme === "light" ? Colors.LIGHT_GRAY5 : Colors.DARK_GRAY2,
            },
          }}
        />
      </div>
      {retrieve && (
        <CustomMarker
          key={retrieve.id}
          theme={theme}
          longitude={retrieve.geometry.coordinates[0]}
          latitude={retrieve.geometry.coordinates[1]}
        />
      )}
    </>
  );
}
