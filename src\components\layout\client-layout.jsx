import { Button, Colors, Menu, MenuItem } from "@blueprintjs/core";
import { Outlet, useNavigate, useOutletContext } from "react-router-dom";

export default function ClientLayout() {
  const { theme, sidebarOpen } = useOutletContext();
  const navigate = useNavigate();
  return (
    <>
      <div
        style={{
          width: sidebarOpen ? 230 : 46,
          borderRight:
            theme === "light"
              ? `1px solid ${Colors.LIGHT_GRAY1}`
              : `1px solid ${Colors.DARK_GRAY5}`,
          paddingBlock: 10,
          overflow: "hidden",
        }}
      >
        <Menu
          style={{ padding: 0, backgroundColor: "transparent" }}
          className="sidebarMenu"
        >
          <MenuItem
            title="Dashboard"
            text={sidebarOpen && "Dashboard"}
            icon="dashboard"
            intent={window.location.pathname === "/" ? "primary" : "none"}
            onClick={() => navigate("")}
          />
        </Menu>
      </div>
      <div
        style={{
          flex: 1,
          overflow: "hidden",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Outlet context={{ theme, sidebarOpen }} />
      </div>
    </>
  );
}
