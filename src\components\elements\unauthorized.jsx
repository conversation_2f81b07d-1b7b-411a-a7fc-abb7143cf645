import { Button, Colors, NonIdealState } from "@blueprintjs/core";
import { useOutletContext } from "react-router-dom";

export default function Unauthorized() {
  const { theme } = useOutletContext();
  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        borderTop:
          theme === "light"
            ? `1px solid ${Colors.LIGHT_GRAY1}`
            : `1px solid ${Colors.DARK_GRAY5}`,
        flex: 1,
      }}
    >
      <NonIdealState
        icon="warning-sign"
        title="Unauthorized"
        description="You are not authorized to view this page."
        action={
          <Button
            text="Back to Home"
            onClick={() => (window.location.href = "/")}
          />
        }
      />
    </div>
  );
}
