import {
  Button,
  Classes,
  Dialog,
  DialogBody,
  DialogFooter,
  FormGroup,
  HTMLSelect,
  InputGroup,
  TextArea,
} from "@blueprintjs/core";
import { useSearchParams } from "react-router-dom";
import { functions } from "../../services/functions";

export default function InternalFormEntityNew({ theme }) {
  const [searchParams, setSearchParams] = useSearchParams();
  return (
    <Dialog
      className={theme === "light" ? Classes.LIGHT : Classes.DARK}
      isOpen={searchParams.get("newEntity") === "true"}
      title="New Entity"
      canOutsideClickClose={false}
      onClose={() => setSearchParams()}
      style={{ width: 400 }}
    >
      <form
        onKeyDown={(event) => {
          if (event.key === "Enter") {
            event.preventDefault();
          }
        }}
        onSubmit={(event) => {
          event.preventDefault();
          functions.data.entities.create(event).then(() => setSearchParams());
        }}
      >
        <DialogBody>
          <FormGroup label="Entity Name" subLabel="Name of the entity">
            <InputGroup name="name" required />
          </FormGroup>
          <FormGroup label="Sector" subLabel="Sector of the entity">
            <HTMLSelect
              name="sector"
              defaultValue={"Logistics"}
              required
              fill
              options={[
                "Logistics",
                "Defence",
                "Aerospace",
                "Railway",
                "Manufacturing",
                "Other",
              ]}
            />
          </FormGroup>
          <FormGroup label="Description" subLabel="Description of the entity">
            <TextArea fill name="description" />
          </FormGroup>
        </DialogBody>
        <DialogFooter
          actions={[
            <Button text="Cancel" key="cancel" />,
            <Button
              text="Create"
              intent="primary"
              type="submit"
              key="create"
            />,
          ]}
        />
      </form>
    </Dialog>
  );
}
