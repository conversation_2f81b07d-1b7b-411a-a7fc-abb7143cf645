import { Layer, Source } from "react-map-gl/mapbox";

export default function InternalSitesHeatmap({ sites, visible = true }) {
  if (!visible) return null;
  return (
    <>
      <Source
        id="sites"
        type="geojson"
        data={{
          type: "FeatureCollection",
          features: sites.map((s) => ({
            type: "Feature",
            geometry: {
              type: "Point",
              coordinates: [
                s.location.geometry.coordinates[0],
                s.location.geometry.coordinates[1],
              ],
            },
            properties: {
              id: s.id,
              name: s.name,
            },
          })),
        }}
      >
        <Layer
          id="heatmap"
          type="heatmap"
          source="sites"
          paint={{
            "heatmap-radius": 20,
            "heatmap-weight": 1,
            "heatmap-intensity": 1,
            "heatmap-color": [
              "interpolate",
              ["linear"],
              ["heatmap-density"],
              0,
              "rgba(0, 0, 255, 0)",
              0.2,
              "rgba(0, 0, 255, 0.5)",
              0.4,
              "rgba(0, 0, 255, 1)",
            ],
            "heatmap-opacity": 0.8,
          }}
        />
      </Source>
    </>
  );
}
