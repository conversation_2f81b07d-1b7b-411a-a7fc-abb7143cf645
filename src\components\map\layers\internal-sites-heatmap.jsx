import { Layer, Source } from "react-map-gl/mapbox";
import { useMemo } from "react";

export default function InternalSitesHeatmap({ sites, visible = true }) {
  if (!visible) return null;

  // Calculer la densité des sites pour optimiser la heatmap
  const heatmapData = useMemo(() => {
    if (!sites || sites.length === 0) {
      return {
        type: "FeatureCollection",
        features: [],
      };
    }

    // Fonction pour calculer la distance entre deux points (en km)
    const calculateDistance = (lat1, lon1, lat2, lon2) => {
      const R = 6371; // Rayon de la Terre en km
      const dLat = ((lat2 - lat1) * Math.PI) / 180;
      const dLon = ((lon2 - lon1) * Math.PI) / 180;
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos((lat1 * Math.PI) / 180) *
          Math.cos((lat2 * Math.PI) / 180) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c;
    };

    // Calculer le poids de chaque site basé sur la proximité des autres sites
    const sitesWithWeight = sites.map((site) => {
      const lat = site.location.geometry.coordinates[1];
      const lon = site.location.geometry.coordinates[0];

      // Compter les sites dans un rayon de 50km
      const nearbyRadius = 50; // km
      const nearbySites = sites.filter((otherSite) => {
        if (otherSite.id === site.id) return false;

        const otherLat = otherSite.location.geometry.coordinates[1];
        const otherLon = otherSite.location.geometry.coordinates[0];
        const distance = calculateDistance(lat, lon, otherLat, otherLon);

        return distance <= nearbyRadius;
      });

      // Calculer le poids basé sur la densité locale
      // Plus il y a de sites proches, plus le poids est élevé
      const baseWeight = 1;
      const densityMultiplier = Math.min(nearbySites.length * 0.5, 5); // Max 5x le poids de base
      const weight = baseWeight + densityMultiplier;

      return {
        ...site,
        weight: weight,
        nearbyCount: nearbySites.length,
      };
    });

    return {
      type: "FeatureCollection",
      features: sitesWithWeight.map((s) => ({
        type: "Feature",
        geometry: {
          type: "Point",
          coordinates: [
            s.location.geometry.coordinates[0],
            s.location.geometry.coordinates[1],
          ],
        },
        properties: {
          id: s.id,
          name: s.name,
          weight: s.weight,
          nearbyCount: s.nearbyCount,
        },
      })),
    };
  }, [sites]);

  return (
    <>
      <Source id="sites-heatmap" type="geojson" data={heatmapData}>
        <Layer
          id="heatmap-density"
          type="heatmap"
          source="sites-heatmap"
          paint={{
            // Rayon adaptatif selon le zoom - diminue aux zooms élevés
            "heatmap-radius": [
              "interpolate",
              ["linear"],
              ["zoom"],
              0,
              20, // Rayon moyen aux faibles zooms
              5,
              30, // Rayon maximum aux zooms moyens
              10,
              25, // Commence à diminuer
              12,
              15, // Rayon réduit aux zooms élevés
              15,
              8, // Rayon très petit pour la précision
              18,
              5, // Rayon minimal aux zooms très élevés
            ],
            // Poids basé sur la densité calculée avec réduction aux zooms élevés
            "heatmap-weight": [
              "case",
              // Aux zooms élevés (>12), réduire drastiquement le poids
              [">=", ["zoom"], 12],
              [
                "interpolate",
                ["linear"],
                ["get", "weight"],
                1,
                0.1, // Poids très faible pour sites isolés
                3,
                0.3, // Poids faible pour sites avec voisins
                6,
                0.6, // Poids modéré pour zones denses
              ],
              // Aux zooms moyens et faibles, poids normal
              [
                "interpolate",
                ["linear"],
                ["get", "weight"],
                1,
                0.5, // Poids minimum pour les sites isolés
                3,
                1.5, // Poids moyen pour les sites avec quelques voisins
                6,
                3, // Poids maximum pour les zones très denses
              ],
            ],
            // Intensité qui diminue fortement aux zooms élevés
            "heatmap-intensity": [
              "interpolate",
              ["linear"],
              ["zoom"],
              0,
              1.0, // Intensité normale aux faibles zooms
              5,
              1.5, // Intensité élevée aux zooms moyens
              8,
              1.2, // Commence à diminuer
              10,
              0.8, // Intensité réduite
              12,
              0.4, // Intensité faible aux zooms élevés
              15,
              0.2, // Intensité très faible
              18,
              0.1, // Intensité minimale aux zooms très élevés
            ],
            // Gradient de couleur amélioré pour une meilleure lisibilité
            "heatmap-color": [
              "interpolate",
              ["linear"],
              ["heatmap-density"],
              0,
              "rgba(255, 255, 255, 0)", // Transparent
              0.1,
              "rgba(65, 105, 225, 0.1)", // Bleu très léger
              0.3,
              "rgba(65, 105, 225, 0.4)", // Bleu léger
              0.5,
              "rgba(255, 165, 0, 0.6)", // Orange moyen
              0.7,
              "rgba(255, 69, 0, 0.8)", // Rouge-orange
              1,
              "rgba(220, 20, 60, 1)", // Rouge intense
            ],
            // Opacité qui diminue aux zooms élevés pour éviter la surcharge visuelle
            "heatmap-opacity": [
              "interpolate",
              ["linear"],
              ["zoom"],
              0,
              0.7, // Opacité normale aux faibles zooms
              5,
              0.9, // Opacité maximale aux zooms moyens
              8,
              0.8, // Commence à diminuer
              10,
              0.6, // Opacité réduite
              12,
              0.4, // Opacité faible aux zooms élevés
              14,
              0.2, // Très transparent
              16,
              0.1, // Presque invisible
              18,
              0, // Complètement masqué aux zooms très élevés
            ],
          }}
        />
      </Source>
    </>
  );
}
