import { Layer, Source } from "react-map-gl/mapbox";
import { useMemo } from "react";

export default function InternalSitesHeatmap({ sites, visible = true }) {
  if (!visible) return null;

  // Calculer la densité des sites pour optimiser la heatmap
  const heatmapData = useMemo(() => {
    if (!sites || sites.length === 0) {
      return {
        type: "FeatureCollection",
        features: [],
      };
    }

    // Fonction pour calculer la distance entre deux points (en km)
    const calculateDistance = (lat1, lon1, lat2, lon2) => {
      const R = 6371; // Rayon de la Terre en km
      const dLat = ((lat2 - lat1) * Math.PI) / 180;
      const dLon = ((lon2 - lon1) * Math.PI) / 180;
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos((lat1 * Math.PI) / 180) *
          Math.cos((lat2 * Math.PI) / 180) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c;
    };

    // Calculer le poids de chaque site basé sur la proximité des autres sites
    const sitesWithWeight = sites.map((site) => {
      const lat = site.location.geometry.coordinates[1];
      const lon = site.location.geometry.coordinates[0];

      // Compter les sites dans un rayon de 50km
      const nearbyRadius = 50; // km
      const nearbySites = sites.filter((otherSite) => {
        if (otherSite.id === site.id) return false;

        const otherLat = otherSite.location.geometry.coordinates[1];
        const otherLon = otherSite.location.geometry.coordinates[0];
        const distance = calculateDistance(lat, lon, otherLat, otherLon);

        return distance <= nearbyRadius;
      });

      // Calculer le poids basé sur la densité locale
      // Plus il y a de sites proches, plus le poids est élevé
      const baseWeight = 1;
      const densityMultiplier = Math.min(nearbySites.length * 0.5, 5); // Max 5x le poids de base
      const weight = baseWeight + densityMultiplier;

      return {
        ...site,
        weight: weight,
        nearbyCount: nearbySites.length,
      };
    });

    return {
      type: "FeatureCollection",
      features: sitesWithWeight.map((s) => ({
        type: "Feature",
        geometry: {
          type: "Point",
          coordinates: [
            s.location.geometry.coordinates[0],
            s.location.geometry.coordinates[1],
          ],
        },
        properties: {
          id: s.id,
          name: s.name,
          weight: s.weight,
          nearbyCount: s.nearbyCount,
        },
      })),
    };
  }, [sites]);

  return (
    <>
      <Source id="sites-heatmap" type="geojson" data={heatmapData}>
        <Layer
          id="heatmap-density"
          type="heatmap"
          source="sites-heatmap"
          paint={{
            // Rayon adaptatif selon le zoom
            "heatmap-radius": [
              "interpolate",
              ["linear"],
              ["zoom"],
              0,
              15, // Rayon plus petit aux faibles zooms
              5,
              25, // Rayon moyen aux zooms intermédiaires
              10,
              40, // Rayon plus grand aux zooms élevés
              15,
              60, // Rayon maximum pour les détails
            ],
            // Poids basé sur la densité calculée
            "heatmap-weight": [
              "interpolate",
              ["linear"],
              ["get", "weight"],
              1,
              0.5, // Poids minimum pour les sites isolés
              3,
              1.5, // Poids moyen pour les sites avec quelques voisins
              6,
              3, // Poids maximum pour les zones très denses
            ],
            // Intensité adaptée au niveau de zoom
            "heatmap-intensity": [
              "interpolate",
              ["linear"],
              ["zoom"],
              0,
              0.8, // Intensité réduite aux faibles zooms
              5,
              1.2, // Intensité normale aux zooms moyens
              10,
              1.8, // Intensité élevée aux zooms élevés
              15,
              2.5, // Intensité maximum pour les détails
            ],
            // Gradient de couleur amélioré pour une meilleure lisibilité
            "heatmap-color": [
              "interpolate",
              ["linear"],
              ["heatmap-density"],
              0,
              "rgba(255, 255, 255, 0)", // Transparent
              0.1,
              "rgba(65, 105, 225, 0.1)", // Bleu très léger
              0.3,
              "rgba(65, 105, 225, 0.4)", // Bleu léger
              0.5,
              "rgba(255, 165, 0, 0.6)", // Orange moyen
              0.7,
              "rgba(255, 69, 0, 0.8)", // Rouge-orange
              1,
              "rgba(220, 20, 60, 1)", // Rouge intense
            ],
            // Opacité globale
            "heatmap-opacity": [
              "interpolate",
              ["linear"],
              ["zoom"],
              0,
              0.6, // Plus transparent aux faibles zooms
              5,
              0.8, // Opacité normale aux zooms moyens
              15,
              0.9, // Plus opaque aux zooms élevés
            ],
          }}
        />
      </Source>
    </>
  );
}
