import {
  But<PERSON>,
  <PERSON>es,
  Dialog,
  DialogBody,
  <PERSON>alog<PERSON><PERSON>er,
  Drawer,
  FormGroup,
  HTMLSelect,
  Icon,
  InputGroup,
} from "@blueprintjs/core";
import { useNavigate, useSearchParams } from "react-router-dom";
import { functions } from "../../services/functions";
import { useState } from "react";

export default function InternalFormCustomerNew({ theme }) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);

  const password = Math.random().toString(36).slice(-8);
  return (
    <Dialog
      className={theme === "light" ? Classes.LIGHT : Classes.DARK}
      isOpen={searchParams.get("newUser") === "true"}
      title="New User"
      canOutsideClickClose={false}
      onClose={() => setSearchParams()}
      style={{ width: 400 }}
    >
      <form
        onSubmit={(event) => {
          setLoading(true);
          functions.data.users.internal
            .create(event)
            .then(() => {
              setLoading(false);
              setSearchParams();
            })
            .catch((error) => {
              setLoading(false);
            });
        }}
      >
        <DialogBody>
          <div style={{ display: "flex", gap: 15 }}>
            <FormGroup label="First Name" style={{ flex: 1 }}>
              <InputGroup
                name="firstName"
                required
                type="text"
                disabled={loading}
              />
            </FormGroup>
            <FormGroup label="Last Name" style={{ flex: 1 }}>
              <InputGroup
                name="lastName"
                required
                type="text"
                disabled={loading}
              />
            </FormGroup>
          </div>
          <FormGroup label="Email">
            <InputGroup name="email" type="email" required disabled={loading} />
          </FormGroup>
          <FormGroup label="Password">
            <InputGroup
              name="password"
              type="text"
              value={password}
              disabled
              rightElement={
                <Button
                  icon="clipboard"
                  onClick={() => navigator.clipboard.writeText(password)}
                />
              }
            />
          </FormGroup>
          <div style={{ display: "flex", gap: 15 }}>
            <FormGroup label="Type" style={{ flex: 1 }}>
              <HTMLSelect
                disabled={loading}
                fill
                name="type"
                defaultValue={"external"}
                required
                options={["internal", "external"]}
              />
            </FormGroup>
            <FormGroup label="Role" style={{ flex: 1 }}>
              <HTMLSelect
                disabled={loading}
                fill
                name="role"
                defaultValue={"basic"}
                required
                options={["basic", "admin"]}
              />
            </FormGroup>
          </div>
        </DialogBody>
        <DialogFooter
          actions={[
            <Button
              text="Cancel"
              onClick={() => setSearchParams()}
              key="cancel"
              loading={loading}
            />,
            <Button
              text="Create"
              intent="primary"
              type="submit"
              key="create"
              loading={loading}
            />,
          ]}
        />
      </form>
    </Dialog>
  );
}
