import { Colors, EntityTitle, H6, Switch } from "@blueprintjs/core";

export default function InternalRightSidebar({
  mapRef,
  theme,
  center,
  display,
  setDisplay,
}) {
  return (
    <>
      <div style={{ display: "flex", flexFlow: "column", flex: 1 }}>
        <div style={{ flex: 1 }}>
          <div style={{ padding: 20 }}>
            <EntityTitle title={"Display"} icon="layers" heading={H6} />
            <div style={{ paddingBlock: 10 }}>
              <Switch
                alignIndicator="right"
                checked={display.includes("workforcesMarkers")}
                onChange={() =>
                  setDisplay(
                    !display.includes("workforcesMarkers")
                      ? [...display, "workforcesMarkers"]
                      : display.filter((d) => d !== "workforcesMarkers")
                  )
                }
              >
                Workforces Markers
              </Switch>
              <Switch
                alignIndicator="right"
                checked={display.includes("workforcesCluster")}
                onChange={() =>
                  setDisplay(
                    !display.includes("workforcesCluster")
                      ? [...display, "workforcesCluster"]
                      : display.filter((d) => d !== "workforcesCluster")
                  )
                }
              >
                Workforces Cluster
              </Switch>
              <Switch
                alignIndicator="right"
                checked={display.includes("sitesMarkers")}
                onChange={() =>
                  setDisplay(
                    !display.includes("sitesMarkers")
                      ? [...display, "sitesMarkers"]
                      : display.filter((d) => d !== "sitesMarkers")
                  )
                }
              >
                Sites Markers
              </Switch>
              <Switch
                alignIndicator="right"
                checked={display.includes("sitesCluster")}
                onChange={() =>
                  setDisplay(
                    !display.includes("sitesCluster")
                      ? [...display, "sitesCluster"]
                      : display.filter((d) => d !== "sitesCluster")
                  )
                }
              >
                Sites Cluster
              </Switch>
            </div>
          </div>
        </div>
        <div
          style={{
            paddingInline: 20,
            paddingBlock: 10,
            backgroundColor:
              theme === "light" ? Colors.LIGHT_GRAY4 : Colors.DARK_GRAY2,
            borderTop: `1px solid ${
              theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
            }`,
            display: "flex",
            gap: 15,
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <EntityTitle
            title={mapRef.current?.getCenter().lat.toPrecision(6)}
            subtitle="Latitude"
          />
          <EntityTitle
            title={mapRef.current?.getCenter().lng.toPrecision(6)}
            subtitle="Longitude"
          />
          <EntityTitle
            title={mapRef.current?.getZoom().toPrecision(3)}
            subtitle={"Zoom"}
          />
        </div>
      </div>
    </>
  );
}
