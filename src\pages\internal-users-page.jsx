import {
  Outlet,
  useNavigate,
  useOutletContext,
  useSearchParams,
} from "react-router-dom";
import PageTitle from "../components/elements/page-title";
import {
  Button,
  Card,
  Colors,
  EntityTitle,
  H3,
  HTMLTable,
  InputGroup,
} from "@blueprintjs/core";
import sortBy from "sort-by";
import { functions } from "../services/functions";
import { useState } from "react";
import { CSVDownload, CSVLink } from "react-csv";
import dayjs from "dayjs";

export default function InternalUsersPage() {
  const { users, fireUsers, theme } = useOutletContext();
  const [searchParams, setSearchParams] = useSearchParams();
  const [search, setSearch] = useState("");
  return (
    <>
      <PageTitle
        title="Users"
        actions={
          <>
            <CSVLink
              filename={`users-${dayjs().format("DD-MM-YYYY HH:mm:ss")}.csv`}
              data={users.map((u) => ({
                email: u.email,
                firstName: functions.tools.crypt.decrypt(
                  fireUsers.find((fu) => fu.id === u.uid).firstName
                ),
                lastName: functions.tools.crypt.decrypt(
                  fireUsers.find((fu) => fu.id === u.uid).lastName
                ),
                "email verified": u.emailVerified,
                created: dayjs(u.metadata.creationTime).format(
                  "DD/MM/YYYY HH:mm:ss"
                ),
                "last login": dayjs(u.metadata.lastSignInTime).format(
                  "DD/MM/YYYY HH:mm:ss"
                ),
                type: fireUsers.find((fu) => fu.id === u.uid).type,
                role: fireUsers.find((fu) => fu.id === u.uid).role,
              }))}
            >
              <Button icon="export" text="Export" />
            </CSVLink>
            <Button
              icon="add"
              text="Add User"
              intent="primary"
              onClick={() => setSearchParams({ newUser: "true" })}
            />
          </>
        }
      />
      <div
        style={{
          paddingInline: "40px 20px",
          paddingBlock: "0px 30px",
          display: "flex",
          gap: 15,
        }}
      >
        <InputGroup
          onChange={(e) => setSearch(e.target.value)}
          placeholder="Search"
          leftIcon="search"
        />
      </div>
      <div
        style={{
          flex: 1,
          paddingInline: "40px 20px",
        }}
      >
        <HTMLTable
          interactive
          width={"100%"}
          style={{
            border: `1px solid ${
              theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
            }`,
          }}
          bordered
        >
          <thead>
            <tr>
              <th
                style={{ cursor: "pointer" }}
                onClick={() => {
                  if (searchParams.get("sort") !== "email") {
                    setSearchParams({ sort: "email" });
                  } else {
                    setSearchParams({ sort: "-email" });
                  }
                }}
              >
                Email
                {searchParams.get("sort") === "email"
                  ? "▵"
                  : searchParams.get("sort") === "-email"
                  ? "▿"
                  : null}
              </th>
              <th>Email Verified</th>
              <th
                style={{ cursor: "pointer" }}
                onClick={() => {
                  if (searchParams.get("sort") !== "metadata.creationTime") {
                    setSearchParams({ sort: "metadata.creationTime" });
                  } else {
                    setSearchParams({ sort: "-metadata.creationTime" });
                  }
                }}
              >
                Created
                {searchParams.get("sort") === "metadata.creationTime"
                  ? "▵"
                  : searchParams.get("sort") === "-metadata.creationTime"
                  ? "▿"
                  : null}
              </th>
              <th
                style={{ cursor: "pointer" }}
                onClick={() => {
                  if (searchParams.get("sort") !== "metadata.lastSignInTime") {
                    setSearchParams({ sort: "metadata.lastSignInTime" });
                  } else {
                    setSearchParams({ sort: "-metadata.lastSignInTime" });
                  }
                }}
              >
                Last Login{" "}
                {searchParams.get("sort") === "metadata.lastSignInTime"
                  ? "▵"
                  : searchParams.get("sort") === "-metadata.lastSignInTime"
                  ? "▿"
                  : null}{" "}
              </th>
              <th>Type</th>
              <th>Role</th>
            </tr>
          </thead>
          <tbody>
            {users
              .filter((user) => {
                return (
                  user.email?.includes(search) ||
                  user.metadata.creationTime?.includes(search) ||
                  user.metadata.lastSignInTime?.includes(search)
                );
              })
              .sort(sortBy(searchParams.get("sort")))
              .map((user) => (
                <tr
                  key={user.uid}
                  onClick={() =>
                    setSearchParams({ userView: "true", user: user.uid })
                  }
                >
                  <td>{user.email}</td>
                  <td>{user.emailVerified ? "Yes" : "No"}</td>
                  <td>{user.metadata.creationTime}</td>
                  <td>{user.metadata.lastSignInTime}</td>
                  <td>
                    {fireUsers.find((u) => u.id === user.uid).type ||
                      "undefined"}
                  </td>
                  <td>
                    {fireUsers.find((u) => u.id === user.uid).role ||
                      "undefined"}
                  </td>
                </tr>
              ))}
          </tbody>
        </HTMLTable>
      </div>
      <Outlet />
    </>
  );
}
