import { Button, Colors, FormGroup, InputGroup } from "@blueprintjs/core";
import { functions } from "../services/functions";

export default function GlobalSigninPage() {
  return (
    <>
      <div
        style={{
          display: "flex",
          minHeight: "100svh",
        }}
      >
        <div
          style={{
            paddingInline: 40,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            flex: 1,
          }}
        >
          <div style={{ width: 260 }}>
            <div style={{ marginBottom: 30 }}>
              <div style={{ fontSize: 20, fontWeight: 600 }}>Welcome back!</div>
              <div>Add your credentials to sign in.</div>
            </div>
            <form onSubmit={(e) => functions.auth.signin(e)}>
              <FormGroup label="Email address">
                <InputGroup name="email" type="email" required />
              </FormGroup>
              <FormGroup
                label="Password"
                helperText={
                  <div style={{ textAlign: "right" }}>
                    <span style={{ cursor: "pointer" }}>
                      Forgot your password?
                    </span>
                  </div>
                }
              >
                <InputGroup name="password" type="password" required />
              </FormGroup>
              <Button
                type="submit"
                text="Sign in"
                intent="primary"
                fill
                style={{
                  marginTop: 30,
                }}
              />
            </form>
          </div>
        </div>
        <div
          style={{
            flex: 2,
            backgroundColor: Colors.LIGHT_GRAY5,
            borderLeft: `1px solid ${Colors.LIGHT_GRAY1}`,
          }}
        ></div>
      </div>
    </>
  );
}
