import { Drawer } from "@blueprintjs/core";
import { useOutletContext, useSearchParams } from "react-router-dom";

export default function InternalUserView({ theme, users, fireUsers }) {
  const [searchParams, setSearchParams] = useSearchParams();

  const user = users.find((u) => u.uid === searchParams.get("user"));
  const fireUser = fireUsers.find((u) => u.id === searchParams.get("user"));
  return (
    <>
      <Drawer
        isOpen={searchParams.get("userView") === "true"}
        onClose={() => setSearchParams()}
        title={user?.email}
      ></Drawer>
    </>
  );
}
